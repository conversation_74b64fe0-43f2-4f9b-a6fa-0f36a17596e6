#!/usr/bin/env node

/**
 * Simple CLI wrapper for database cleanup
 * This file can be run directly from the project root
 */

require('dotenv').config({ path: '.env.local' });
const DatabaseCleaner = require('./src/scripts/database-cleanup');

async function main() {
  const args = process.argv.slice(2);
  
  console.log('🌈 Rainbow Paws Database Cleanup Tool');
  console.log('=====================================\n');
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage:
  node cleanup-database.js [options]

Options:
  --dry-run     Preview what will be cleaned (default)
  --execute     Actually perform the cleanup
  --help, -h    Show this help message

Examples:
  node cleanup-database.js                    # Preview cleanup
  node cleanup-database.js --dry-run          # Preview cleanup  
  node cleanup-database.js --execute          # Perform cleanup

What this script cleans:
  ✅ Corrupted data (malformed records)
  ✅ Old log entries (older than 6 months)
  ✅ Expired password reset tokens
  ✅ Expired OTP codes and attempts
  ✅ Old rate limit entries
  ✅ Database optimization

What this script preserves:
  🔒 All user data
  🔒 All business data (bookings, payments, etc.)
  🔒 Recent logs and notifications
  🔒 All database tables and structure
`);
    return;
  }

  const isDryRun = !args.includes('--execute');
  
  if (isDryRun) {
    console.log('🔍 PREVIEW MODE - No changes will be made to the database');
    console.log('Use --execute flag to perform actual cleanup\n');
  } else {
    console.log('⚠️  EXECUTION MODE - Changes will be made to the database');
    console.log('Press Ctrl+C within 5 seconds to cancel...\n');
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  const cleaner = new DatabaseCleaner(isDryRun);
  const success = await cleaner.run();
  
  if (success && isDryRun) {
    console.log('\n💡 To perform the actual cleanup, run:');
    console.log('   node cleanup-database.js --execute');
  }
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}
