# Rainbow Paws Database Cleanup Script for Windows
# Usage: .\cleanup-database.ps1 [-Execute] [-Help]

param(
    [switch]$Execute,
    [switch]$Help
)

if ($Help) {
    Write-Host "🌈 Rainbow Paws Database Cleanup Tool" -ForegroundColor Cyan
    Write-Host "=====================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\cleanup-database.ps1           # Preview cleanup (default)"
    Write-Host "  .\cleanup-database.ps1 -Execute  # Perform actual cleanup"
    Write-Host "  .\cleanup-database.ps1 -Help     # Show this help"
    Write-Host ""
    Write-Host "What this script cleans:" -ForegroundColor Green
    Write-Host "  ✅ Corrupted data (malformed records)"
    Write-Host "  ✅ Old log entries (older than 6 months)"
    Write-Host "  ✅ Expired password reset tokens"
    Write-Host "  ✅ Expired OTP codes and attempts"
    Write-Host "  ✅ Old rate limit entries"
    Write-Host "  ✅ Database optimization"
    Write-Host ""
    Write-Host "What this script preserves:" -ForegroundColor Red
    Write-Host "  🔒 All user data"
    Write-Host "  🔒 All business data (bookings, payments, etc.)"
    Write-Host "  🔒 Recent logs and notifications"
    Write-Host "  🔒 All database tables and structure"
    exit 0
}

Write-Host "🌈 Rainbow Paws Database Cleanup Tool" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js first." -ForegroundColor Red
    exit 1
}

# Check if the cleanup script exists
if (-not (Test-Path "cleanup-database.js")) {
    Write-Host "❌ cleanup-database.js not found. Make sure you're in the project root directory." -ForegroundColor Red
    exit 1
}

# Prepare the command
$command = "node cleanup-database.js"
if ($Execute) {
    $command += " --execute"
    Write-Host "⚠️  EXECUTION MODE - Changes will be made to the database" -ForegroundColor Yellow
    Write-Host "Press Ctrl+C within 5 seconds to cancel..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
} else {
    Write-Host "🔍 PREVIEW MODE - No changes will be made to the database" -ForegroundColor Green
    Write-Host "Use -Execute flag to perform actual cleanup" -ForegroundColor Green
}

Write-Host ""

# Execute the cleanup
try {
    Invoke-Expression $command
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -eq 0) {
        Write-Host ""
        if (-not $Execute) {
            Write-Host "💡 To perform the actual cleanup, run:" -ForegroundColor Cyan
            Write-Host "   .\cleanup-database.ps1 -Execute" -ForegroundColor Cyan
        }
    } else {
        Write-Host "❌ Cleanup script failed with exit code: $exitCode" -ForegroundColor Red
    }
    
    exit $exitCode
} catch {
    Write-Host "❌ Failed to execute cleanup script: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
