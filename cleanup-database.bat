@echo off
setlocal enabledelayedexpansion

REM Rainbow Paws Database Cleanup Script for Windows
REM Usage: cleanup-database.bat [execute|help]

if "%1"=="help" goto :help
if "%1"=="--help" goto :help
if "%1"=="-h" goto :help

echo 🌈 Rainbow Paws Database Cleanup Tool
echo =====================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found. Please install Node.js first.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js found: !NODE_VERSION!

REM Check if the cleanup script exists
if not exist "cleanup-database.js" (
    echo ❌ cleanup-database.js not found. Make sure you're in the project root directory.
    pause
    exit /b 1
)

REM Prepare the command
set COMMAND=node cleanup-database.js
if "%1"=="execute" (
    set COMMAND=!COMMAND! --execute
    echo ⚠️  EXECUTION MODE - Changes will be made to the database
    echo Press Ctrl+C within 5 seconds to cancel...
    timeout /t 5 /nobreak >nul
) else (
    echo 🔍 PREVIEW MODE - No changes will be made to the database
    echo Use "cleanup-database.bat execute" to perform actual cleanup
)

echo.

REM Execute the cleanup
!COMMAND!
set EXIT_CODE=!errorlevel!

echo.
if !EXIT_CODE! equ 0 (
    if not "%1"=="execute" (
        echo 💡 To perform the actual cleanup, run:
        echo    cleanup-database.bat execute
    )
) else (
    echo ❌ Cleanup script failed with exit code: !EXIT_CODE!
)

pause
exit /b !EXIT_CODE!

:help
echo 🌈 Rainbow Paws Database Cleanup Tool
echo =====================================
echo.
echo Usage:
echo   cleanup-database.bat           # Preview cleanup (default)
echo   cleanup-database.bat execute   # Perform actual cleanup
echo   cleanup-database.bat help      # Show this help
echo.
echo What this script cleans:
echo   ✅ Corrupted data (malformed records)
echo   ✅ Old log entries (older than 6 months)
echo   ✅ Expired password reset tokens
echo   ✅ Expired OTP codes and attempts
echo   ✅ Old rate limit entries
echo   ✅ Database optimization
echo.
echo What this script preserves:
echo   🔒 All user data
echo   🔒 All business data (bookings, payments, etc.)
echo   🔒 Recent logs and notifications
echo   🔒 All database tables and structure
echo.
pause
exit /b 0
