#!/usr/bin/env node

/**
 * Rainbow Paws Database Cleanup Script
 * 
 * This script safely cleans up unused and corrupted data from the database
 * while preserving all user data and business-critical information.
 * 
 * Usage:
 *   node src/scripts/database-cleanup.js --dry-run    # Preview what will be cleaned
 *   node src/scripts/database-cleanup.js --execute   # Actually perform cleanup
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'rainbow_paws',
  port: parseInt(process.env.DB_PORT || '3306'),
  multipleStatements: true
};

class DatabaseCleaner {
  constructor(dryRun = true) {
    this.dryRun = dryRun;
    this.connection = null;
    this.cleanupLog = [];
    this.stats = {
      corruptedRecords: 0,
      oldLogEntries: 0,
      expiredTokens: 0,
      expiredOtpCodes: 0,
      oldRateLimits: 0,
      totalCleaned: 0
    };
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(dbConfig);
      console.log('✅ Connected to database successfully');
      return true;
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
      console.log('✅ Database connection closed');
    }
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
    this.cleanupLog.push(logEntry);
    
    const emoji = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${emoji} ${message}`);
  }

  async executeQuery(query, params = []) {
    if (this.dryRun) {
      this.log(`DRY RUN - Would execute: ${query.substring(0, 100)}...`, 'info');
      return { affectedRows: 0 };
    }
    
    try {
      const [result] = await this.connection.execute(query, params);
      return result;
    } catch (error) {
      this.log(`Query failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async cleanCorruptedData() {
    this.log('🧹 Cleaning corrupted data...', 'info');
    
    // Check for corrupted pet records
    const [corruptedPets] = await this.connection.execute(`
      SELECT pet_id, name, species, breed 
      FROM pets 
      WHERE name LIKE '%@%' OR species LIKE '%@%' OR breed LIKE '%@%'
      OR LENGTH(name) > 100 OR LENGTH(species) > 100 OR LENGTH(breed) > 100
    `);

    if (corruptedPets.length > 0) {
      this.log(`Found ${corruptedPets.length} corrupted pet records`, 'warning');
      
      for (const pet of corruptedPets) {
        this.log(`Removing corrupted pet record: ID ${pet.pet_id}`, 'warning');
        await this.executeQuery('DELETE FROM pets WHERE pet_id = ?', [pet.pet_id]);
        this.stats.corruptedRecords++;
      }
    } else {
      this.log('No corrupted pet records found', 'success');
    }
  }

  async cleanOldLogEntries() {
    this.log('🧹 Cleaning old log entries...', 'info');
    
    // Keep logs from last 6 months only
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    // Clean admin_logs
    const [adminLogsResult] = await this.connection.execute(`
      SELECT COUNT(*) as count FROM admin_logs WHERE created_at < ?
    `, [sixMonthsAgo]);
    
    if (adminLogsResult[0].count > 0) {
      this.log(`Removing ${adminLogsResult[0].count} old admin log entries`, 'info');
      await this.executeQuery('DELETE FROM admin_logs WHERE created_at < ?', [sixMonthsAgo]);
      this.stats.oldLogEntries += adminLogsResult[0].count;
    }

    // Clean email_log
    const [emailLogsResult] = await this.connection.execute(`
      SELECT COUNT(*) as count FROM email_log WHERE sent_at < ?
    `, [sixMonthsAgo]);
    
    if (emailLogsResult[0].count > 0) {
      this.log(`Removing ${emailLogsResult[0].count} old email log entries`, 'info');
      await this.executeQuery('DELETE FROM email_log WHERE sent_at < ?', [sixMonthsAgo]);
      this.stats.oldLogEntries += emailLogsResult[0].count;
    }
  }

  async cleanExpiredTokens() {
    this.log('🧹 Cleaning expired tokens...', 'info');
    
    // Clean expired password reset tokens
    const [expiredTokensResult] = await this.connection.execute(`
      SELECT COUNT(*) as count FROM password_reset_tokens 
      WHERE expires_at < NOW() OR is_used = 1
    `);
    
    if (expiredTokensResult[0].count > 0) {
      this.log(`Removing ${expiredTokensResult[0].count} expired/used password reset tokens`, 'info');
      await this.executeQuery(`
        DELETE FROM password_reset_tokens 
        WHERE expires_at < NOW() OR is_used = 1
      `);
      this.stats.expiredTokens += expiredTokensResult[0].count;
    }
  }

  async cleanExpiredOtpCodes() {
    this.log('🧹 Cleaning expired OTP codes...', 'info');
    
    // Clean expired OTP codes
    const [expiredOtpResult] = await this.connection.execute(`
      SELECT COUNT(*) as count FROM otp_codes 
      WHERE expires_at < NOW() OR is_used = 1
    `);
    
    if (expiredOtpResult[0].count > 0) {
      this.log(`Removing ${expiredOtpResult[0].count} expired/used OTP codes`, 'info');
      await this.executeQuery(`
        DELETE FROM otp_codes 
        WHERE expires_at < NOW() OR is_used = 1
      `);
      this.stats.expiredOtpCodes += expiredOtpResult[0].count;
    }

    // Clean old OTP attempts (older than 24 hours)
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    
    const [oldOtpAttemptsResult] = await this.connection.execute(`
      SELECT COUNT(*) as count FROM otp_attempts WHERE attempt_time < ?
    `, [oneDayAgo]);
    
    if (oldOtpAttemptsResult[0].count > 0) {
      this.log(`Removing ${oldOtpAttemptsResult[0].count} old OTP attempts`, 'info');
      await this.executeQuery('DELETE FROM otp_attempts WHERE attempt_time < ?', [oneDayAgo]);
      this.stats.expiredOtpCodes += oldOtpAttemptsResult[0].count;
    }
  }

  async cleanOldRateLimits() {
    this.log('🧹 Cleaning old rate limit entries...', 'info');
    
    // Clean rate limits older than 24 hours
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    
    const [oldRateLimitsResult] = await this.connection.execute(`
      SELECT COUNT(*) as count FROM rate_limits WHERE window_start < ?
    `, [oneDayAgo]);
    
    if (oldRateLimitsResult[0].count > 0) {
      this.log(`Removing ${oldRateLimitsResult[0].count} old rate limit entries`, 'info');
      await this.executeQuery('DELETE FROM rate_limits WHERE window_start < ?', [oneDayAgo]);
      this.stats.oldRateLimits += oldRateLimitsResult[0].count;
    }
  }

  async optimizeDatabase() {
    this.log('🔧 Optimizing database tables...', 'info');
    
    const tables = [
      'users', 'service_providers', 'service_packages', 'service_bookings',
      'admin_logs', 'admin_notifications', 'notifications', 'email_log',
      'otp_codes', 'otp_attempts', 'password_reset_tokens', 'rate_limits'
    ];

    for (const table of tables) {
      try {
        if (!this.dryRun) {
          await this.connection.execute(`OPTIMIZE TABLE ${table}`);
        }
        this.log(`Optimized table: ${table}`, 'success');
      } catch (error) {
        this.log(`Failed to optimize table ${table}: ${error.message}`, 'warning');
      }
    }
  }

  async generateReport() {
    this.stats.totalCleaned = 
      this.stats.corruptedRecords + 
      this.stats.oldLogEntries + 
      this.stats.expiredTokens + 
      this.stats.expiredOtpCodes + 
      this.stats.oldRateLimits;

    const report = `
📊 DATABASE CLEANUP REPORT
========================
Mode: ${this.dryRun ? 'DRY RUN (Preview Only)' : 'EXECUTION'}
Date: ${new Date().toISOString()}

🧹 Cleanup Statistics:
- Corrupted records removed: ${this.stats.corruptedRecords}
- Old log entries removed: ${this.stats.oldLogEntries}
- Expired tokens removed: ${this.stats.expiredTokens}
- Expired OTP codes removed: ${this.stats.expiredOtpCodes}
- Old rate limits removed: ${this.stats.oldRateLimits}
- Total items cleaned: ${this.stats.totalCleaned}

${this.dryRun ? '⚠️  This was a preview. Use --execute to perform actual cleanup.' : '✅ Cleanup completed successfully!'}
`;

    console.log(report);
    
    // Save detailed log to file
    const logFile = path.join(__dirname, `cleanup-log-${Date.now()}.txt`);
    const logContent = report + '\n\nDetailed Log:\n' + this.cleanupLog.join('\n');
    
    try {
      await fs.writeFile(logFile, logContent);
      console.log(`📝 Detailed log saved to: ${logFile}`);
    } catch (error) {
      console.log('⚠️  Could not save log file:', error.message);
    }
  }

  async run() {
    try {
      const connected = await this.connect();
      if (!connected) return false;

      this.log(`Starting database cleanup in ${this.dryRun ? 'DRY RUN' : 'EXECUTION'} mode`, 'info');

      await this.cleanCorruptedData();
      await this.cleanOldLogEntries();
      await this.cleanExpiredTokens();
      await this.cleanExpiredOtpCodes();
      await this.cleanOldRateLimits();
      
      if (!this.dryRun) {
        await this.optimizeDatabase();
      }

      await this.generateReport();
      return true;

    } catch (error) {
      this.log(`Cleanup failed: ${error.message}`, 'error');
      return false;
    } finally {
      await this.disconnect();
    }
  }
}

// CLI execution
async function main() {
  const args = process.argv.slice(2);
  const isDryRun = !args.includes('--execute');
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🌈 Rainbow Paws Database Cleanup Tool

Usage:
  node src/scripts/database-cleanup.js [options]

Options:
  --dry-run     Preview what will be cleaned (default)
  --execute     Actually perform the cleanup
  --help, -h    Show this help message

Examples:
  node src/scripts/database-cleanup.js                    # Preview cleanup
  node src/scripts/database-cleanup.js --dry-run          # Preview cleanup
  node src/scripts/database-cleanup.js --execute          # Perform cleanup
`);
    return;
  }

  if (isDryRun) {
    console.log('🔍 Running in DRY RUN mode - no changes will be made');
  } else {
    console.log('⚠️  Running in EXECUTION mode - changes will be made to the database');
    console.log('Press Ctrl+C within 5 seconds to cancel...');
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  const cleaner = new DatabaseCleaner(isDryRun);
  const success = await cleaner.run();
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}

module.exports = DatabaseCleaner;
