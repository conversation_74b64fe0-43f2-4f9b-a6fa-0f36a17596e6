# 🧹 Rainbow Paws Database Cleanup Tool

A comprehensive database cleanup utility that safely removes unused and corrupted data while preserving all user information and business-critical data.

## 🎯 What This Tool Does

### ✅ Cleans Up
- **Corrupted Data**: Removes malformed records (e.g., corrupted pet records with email data)
- **Old Log Entries**: Removes admin logs and email logs older than 6 months
- **Expired Tokens**: <PERSON>moves expired/used password reset tokens
- **Expired OTP Codes**: Removes expired/used OTP codes and old attempts
- **Old Rate Limits**: Removes rate limit entries older than 24 hours
- **Database Optimization**: Optimizes tables for better performance

### 🔒 Preserves
- **All User Data**: Users, profiles, and personal information
- **All Business Data**: Bookings, payments, service providers, packages
- **Recent Logs**: Keeps logs from the last 6 months for audit purposes
- **Database Structure**: No tables or columns are removed
- **Active Sessions**: Current OTP codes and valid tokens

## 🚀 Usage Options

### Option 1: NPM Scripts (Recommended)
```bash
# Preview what will be cleaned (safe)
npm run db:cleanup
npm run db:cleanup:preview

# Actually perform the cleanup
npm run db:cleanup:execute
```

### Option 2: Direct Node.js Execution
```bash
# Preview cleanup
node cleanup-database.js
node cleanup-database.js --dry-run

# Execute cleanup
node cleanup-database.js --execute
```

### Option 3: Windows PowerShell
```powershell
# Preview cleanup
.\cleanup-database.ps1

# Execute cleanup
.\cleanup-database.ps1 -Execute

# Show help
.\cleanup-database.ps1 -Help
```

### Option 4: Windows Batch File
```cmd
# Preview cleanup
cleanup-database.bat

# Execute cleanup
cleanup-database.bat execute

# Show help
cleanup-database.bat help
```

## 📋 Prerequisites

1. **Node.js**: Ensure Node.js is installed
2. **Database Access**: MySQL/MariaDB must be running
3. **Environment Variables**: Ensure `.env.local` is configured with database credentials
4. **Permissions**: Database user must have DELETE and OPTIMIZE privileges

## 🔧 Configuration

The tool uses your existing database configuration from `.env.local`:

```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=rainbow_paws
DB_PORT=3306
```

## 📊 Sample Output

```
🌈 Rainbow Paws Database Cleanup Tool
=====================================

🔍 PREVIEW MODE - No changes will be made to the database
Use --execute flag to perform actual cleanup

✅ Connected to database successfully
🧹 Cleaning corrupted data...
⚠️  Found 1 corrupted pet records
⚠️  Removing corrupted pet record: ID 4
🧹 Cleaning old log entries...
ℹ️  Removing 15 old admin log entries
ℹ️  Removing 25 old email log entries
🧹 Cleaning expired tokens...
ℹ️  Removing 3 expired/used password reset tokens
🧹 Cleaning expired OTP codes...
ℹ️  Removing 8 expired/used OTP codes
ℹ️  Removing 12 old OTP attempts
🧹 Cleaning old rate limit entries...
ℹ️  Removing 5 old rate limit entries

📊 DATABASE CLEANUP REPORT
========================
Mode: DRY RUN (Preview Only)
Date: 2025-07-11T19:30:00.000Z

🧹 Cleanup Statistics:
- Corrupted records removed: 1
- Old log entries removed: 40
- Expired tokens removed: 3
- Expired OTP codes removed: 20
- Old rate limits removed: 5
- Total items cleaned: 69

⚠️  This was a preview. Use --execute to perform actual cleanup.

💡 To perform the actual cleanup, run:
   node cleanup-database.js --execute
```

## 🛡️ Safety Features

1. **Dry Run Mode**: Default mode shows what would be cleaned without making changes
2. **Confirmation Delay**: 5-second delay before execution mode starts
3. **Detailed Logging**: All actions are logged with timestamps
4. **Error Handling**: Graceful error handling with rollback capabilities
5. **Backup Recommendation**: Always backup your database before running cleanup

## 📝 Log Files

The tool generates detailed log files in `src/scripts/` with names like:
- `cleanup-log-1641234567890.txt`

These logs contain:
- Detailed execution report
- All SQL operations performed
- Timestamps for all actions
- Error messages (if any)

## ⚠️ Important Notes

1. **Backup First**: Always backup your database before running the cleanup
2. **Test Environment**: Test the cleanup on a copy of your database first
3. **Peak Hours**: Avoid running during peak usage hours
4. **Monitor Performance**: Database optimization may temporarily lock tables

## 🔍 Troubleshooting

### Database Connection Issues
```bash
❌ Database connection failed: Access denied for user 'root'@'localhost'
```
**Solution**: Check your database credentials in `.env.local`

### Permission Issues
```bash
❌ Query failed: Access denied; you need the DELETE privilege
```
**Solution**: Ensure your database user has DELETE and OPTIMIZE privileges

### Table Not Found
```bash
❌ Query failed: Table 'rainbow_paws.rate_limits' doesn't exist
```
**Solution**: This is normal - the tool will skip cleaning tables that don't exist

## 🔄 Automation

You can automate the cleanup by adding it to your deployment scripts:

```bash
# In your deployment script
npm run db:cleanup:execute
```

Or set up a cron job for regular maintenance:

```bash
# Run cleanup every Sunday at 2 AM
0 2 * * 0 cd /path/to/rainbow-paws && npm run db:cleanup:execute
```

## 📞 Support

If you encounter issues:

1. Check the generated log files for detailed error information
2. Ensure all prerequisites are met
3. Test with `--dry-run` mode first
4. Verify database permissions and connectivity

## 🔐 Security Considerations

- The tool only removes data that is clearly unused or corrupted
- No user-generated content is ever deleted
- All business-critical data is preserved
- Audit logs are kept for compliance (6 months retention)
- Database structure remains unchanged
